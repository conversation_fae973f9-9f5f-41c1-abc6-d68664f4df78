<?php
/**
 * Plugin Name: <PERSON><PERSON><PERSON> Chatbot
 * Plugin URI: https://github.com/JoJoD3v/jojo-wc-chatbot
 * Description: Advanced WooCommerce chatbot with OpenAI integration for order status checking and product recommendations. Features mobile-responsive design and customizable appearance.
 * Version: 0.3.0
 * Author: <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: jojo-wc-chatbot
 * Domain Path: /languages
 * Requires at least: 5.9
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('JOJO_WC_CHATBOT_VERSION', '0.3.0');
define('JOJO_WC_CHATBOT_PLUGIN_FILE', __FILE__);
define('JOJO_WC_CHATBOT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('JOJO_WC_CHATBOT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('JOJO_WC_CHATBOT_INCLUDES_DIR', JOJO_WC_CHATBOT_PLUGIN_DIR . 'includes/');

/**
 * Main Plugin Class
 */
class JoJo_WC_Chatbot {

    /**
     * Single instance of the class
     *
     * @var JoJo_WC_Chatbot
     */
    private static $instance = null;

    /**
     * Admin settings instance
     *
     * @var JoJo_WC_Chatbot_Admin_Settings
     */
    public $admin_settings;

    /**
     * REST API handler instance
     *
     * @var JoJo_WC_Chatbot_REST_API_Handler
     */
    public $rest_api_handler;

    /**
     * Frontend widget instance
     *
     * @var JoJo_WC_Chatbot_Frontend_Widget
     */
    public $frontend_widget;

    /**
     * Get single instance of the class
     *
     * @return JoJo_WC_Chatbot
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('init', array($this, 'init'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Load admin settings class
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'admin/class-admin-settings.php';

        // Load utility classes
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'utils/class-openai-integration.php';
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'utils/class-intent-router.php';

        // Load API handler
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'api/class-rest-api-handler.php';

        // Load frontend widget
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'frontend/class-frontend-widget.php';
    }

    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize admin settings
        if (is_admin()) {
            $this->admin_settings = new JoJo_WC_Chatbot_Admin_Settings();
        }

        // Initialize REST API handler
        $this->rest_api_handler = new JoJo_WC_Chatbot_REST_API_Handler();

        // Initialize frontend widget
        if (!is_admin()) {
            $this->frontend_widget = new JoJo_WC_Chatbot_Frontend_Widget();
        }
    }

    /**
     * Load plugin textdomain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'jojo-wc-chatbot',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Plugin is ready
        do_action('jojo_wc_chatbot_loaded');
    }

    /**
     * Check if WooCommerce is active
     *
     * @return bool WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }

    /**
     * Display admin notice when WooCommerce is missing
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php
                printf(
                    /* translators: %s: WooCommerce plugin name */
                    esc_html__('%1$s requires %2$s to be installed and active.', 'jojo-wc-chatbot'),
                    '<strong>JoJo WC Chatbot</strong>',
                    '<strong>WooCommerce</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }

    /**
     * Plugin activation hook
     */
    public function activate() {
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '5.9', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(
                esc_html__('JoJo WC Chatbot requires WordPress 5.9 or higher.', 'jojo-wc-chatbot'),
                esc_html__('Plugin Activation Error', 'jojo-wc-chatbot'),
                array('back_link' => true)
            );
        }

        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(
                esc_html__('JoJo WC Chatbot requires PHP 7.4 or higher.', 'jojo-wc-chatbot'),
                esc_html__('Plugin Activation Error', 'jojo-wc-chatbot'),
                array('back_link' => true)
            );
        }

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Set activation flag
        update_option('jojo_wc_chatbot_activated', true);
    }

    /**
     * Plugin deactivation hook
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();

        // Clean up temporary data if needed
        delete_transient('jojo_wc_chatbot_api_test');
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            JoJo_WC_Chatbot_Admin_Settings::OPT_MODEL => 'gpt-4o-mini',
            JoJo_WC_Chatbot_Admin_Settings::OPT_BUTTON_TEXT => __('Chat', 'jojo-wc-chatbot'),
            JoJo_WC_Chatbot_Admin_Settings::OPT_AVERAGE_DELIVERY_DAYS => 3,
            JoJo_WC_Chatbot_Admin_Settings::OPT_MAX_DELIVERY_DAYS => 7
        );

        foreach ($defaults as $option => $value) {
            if (get_option($option) === false) {
                update_option($option, $value);
            }
        }
    }

    /**
     * Get plugin version
     *
     * @return string Plugin version
     */
    public static function get_version() {
        return JOJO_WC_CHATBOT_VERSION;
    }

    /**
     * Get plugin directory path
     *
     * @return string Plugin directory path
     */
    public static function get_plugin_dir() {
        return JOJO_WC_CHATBOT_PLUGIN_DIR;
    }

    /**
     * Get plugin URL
     *
     * @return string Plugin URL
     */
    public static function get_plugin_url() {
        return JOJO_WC_CHATBOT_PLUGIN_URL;
    }

    /**
     * Check if plugin is network activated
     *
     * @return bool Is network activated
     */
    public static function is_network_activated() {
        if (!function_exists('is_plugin_active_for_network')) {
            require_once ABSPATH . '/wp-admin/includes/plugin.php';
        }

        return is_plugin_active_for_network(plugin_basename(__FILE__));
    }

}

// Initialize the plugin
function jojo_wc_chatbot_init() {
    return JoJo_WC_Chatbot::get_instance();
}

// Hook into plugins_loaded to ensure all plugins are loaded
add_action('plugins_loaded', 'jojo_wc_chatbot_init');

// Prevent direct instantiation
if (!function_exists('add_action')) {
    echo 'Hi there!  I\'m just a plugin, not much I can do when called directly.';
    exit;
}


