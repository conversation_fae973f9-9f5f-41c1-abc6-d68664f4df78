<?php
/**
 * Intent Router Class
 *
 * Handles intent routing for the chatbot using OpenAI to determine
 * user intent and route to appropriate functions.
 *
 * @package JoJo_WC_Chatbot
 * @since 0.3.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class JoJo_WC_Chatbot_Intent_Router
 */
class JoJo_WC_Chatbot_Intent_Router {

    /**
     * OpenAI integration instance
     *
     * @var JoJo_WC_Chatbot_OpenAI_Integration
     */
    private $openai_integration;

    /**
     * Constructor
     */
    public function __construct() {
        $this->openai_integration = new JoJo_WC_Chatbot_OpenAI_Integration();
    }

    /**
     * Route user message to appropriate intent and function
     *
     * @param string $message User message
     * @return array Response data
     */
    public function route_message($message) {
        // First, determine the intent using OpenAI
        $intent_data = $this->determine_intent($message);
        
        if (!empty($intent_data['error'])) {
            return array("reply" => $intent_data['error']);
        }

        $intent = $intent_data['intent'] ?? 'OTHER';
        $entities = $intent_data['entities'] ?? array();

        // Route to appropriate handler based on intent
        switch ($intent) {
            case 'DELIVERY_TIME':
                return $this->handle_delivery_time();
                
            case 'DELIVERY_ESTIMATE':
                return $this->handle_delivery_estimate($entities, $message);
                
            case 'REFUND_INFO':
                return $this->handle_refund_info();
                
            case 'ORDER_STATUS':
                return $this->handle_order_status($entities, $message);
                
            case 'RECOMMEND_PRODUCTS':
                return $this->handle_product_recommendation($message);
                
            case 'SMALL_TALK':
                return $this->handle_small_talk($message);
                
            default:
                return $this->handle_other($message);
        }
    }

    /**
     * Determine user intent using OpenAI
     *
     * @param string $message User message
     * @return array Intent data
     */
    private function determine_intent($message) {
        // Load the router prompt from the router file
        require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'api/class-rest-router.php';
        
        $router_prompt = $esempio_router ?? '';
        
        if (empty($router_prompt)) {
            return array('error' => 'Router configuration not found.');
        }

        $messages = array(
            array("role" => "system", "content" => $router_prompt),
            array("role" => "user", "content" => $message)
        );

        $response = $this->openai_integration->openai_chat(array(
            "messages" => $messages,
            "temperature" => 0.1,
            "max_tokens" => 150
        ));

        if (!empty($response['error'])) {
            return array('error' => $response['error']);
        }

        $content = $response['choices'][0]['message']['content'] ?? '';
        $intent_data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return array('error' => 'Invalid JSON response from intent router.');
        }

        return $intent_data;
    }

    /**
     * Handle delivery time intent
     *
     * @return array Response data
     */
    private function handle_delivery_time() {
        $average_days = absint(JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_AVERAGE_DELIVERY_DAYS,
            3
        ));

        return array(
            "reply" => sprintf(
                __("I nostri tempi medi di consegna sono di %d giorni lavorativi dalla conferma dell'ordine. Se hai un ordine specifico e vuoi sapere quando arriverà, forniscimi il numero dell'ordine e la tua email o cognome.", 'jojo-wc-chatbot'),
                $average_days
            )
        );
    }

    /**
     * Handle delivery estimate intent
     *
     * @param array $entities Extracted entities
     * @param string $message Original message
     * @return array Response data
     */
    private function handle_delivery_estimate($entities, $message) {
        $order_id = $entities['order_id'] ?? null;
        
        if (!$order_id) {
            return array(
                "reply" => __("Per calcolare la data di consegna prevista, ho bisogno del numero dell'ordine. Puoi fornirmelo insieme alla tua email o cognome per verificare l'ordine?", 'jojo-wc-chatbot')
            );
        }

        // Use the OpenAI integration to handle this with the get_delivery_estimate function
        return $this->openai_integration->process_llm_request($message);
    }

    /**
     * Handle refund info intent
     *
     * @return array Response data
     */
    private function handle_refund_info() {
        $refund_url = esc_url(JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_REFUND_PAGE_URL,
            ''
        ));

        if (empty($refund_url)) {
            return array(
                "reply" => __("Per informazioni sui rimborsi e le procedure di reso, ti consiglio di contattare il nostro servizio clienti che potrà assisterti nel modo migliore.", 'jojo-wc-chatbot')
            );
        }

        return array(
            "reply" => sprintf(
                __("Per informazioni dettagliate sui rimborsi e le procedure di reso, puoi consultare la nostra pagina dedicata: %s\n\nSe hai bisogno di assistenza specifica per un ordine, sarò felice di aiutarti!", 'jojo-wc-chatbot'),
                $refund_url
            )
        );
    }

    /**
     * Handle order status intent
     *
     * @param array $entities Extracted entities
     * @param string $message Original message
     * @return array Response data
     */
    private function handle_order_status($entities, $message) {
        // Use the OpenAI integration to handle this with the get_order_status function
        return $this->openai_integration->process_llm_request($message);
    }

    /**
     * Handle product recommendation intent
     *
     * @param string $message Original message
     * @return array Response data
     */
    private function handle_product_recommendation($message) {
        // Use the OpenAI integration to handle this with the recommend_products function
        return $this->openai_integration->process_llm_request($message);
    }

    /**
     * Handle small talk intent
     *
     * @param string $message Original message
     * @return array Response data
     */
    private function handle_small_talk($message) {
        $responses = array(
            __("Ciao! Come posso aiutarti oggi? Posso fornirti informazioni sui tuoi ordini, consigliarti prodotti o rispondere a domande sui tempi di consegna e rimborsi.", 'jojo-wc-chatbot'),
            __("Salve! Sono qui per assisterti. Posso aiutarti con lo stato degli ordini, suggerimenti sui prodotti o informazioni sui nostri servizi.", 'jojo-wc-chatbot'),
            __("Buongiorno! Sono il tuo assistente virtuale. Come posso esserti utile oggi?", 'jojo-wc-chatbot')
        );

        return array(
            "reply" => $responses[array_rand($responses)]
        );
    }

    /**
     * Handle other/unknown intents
     *
     * @param string $message Original message
     * @return array Response data
     */
    private function handle_other($message) {
        return array(
            "reply" => __("Mi dispiace, non sono sicuro di aver capito la tua richiesta. Posso aiutarti con:\n\n• Stato degli ordini\n• Consigli sui prodotti\n• Tempi di consegna\n• Informazioni sui rimborsi\n\nCosa ti interessa sapere?", 'jojo-wc-chatbot')
        );
    }
}
