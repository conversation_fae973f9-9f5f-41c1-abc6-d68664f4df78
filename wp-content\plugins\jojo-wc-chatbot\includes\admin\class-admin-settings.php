<?php
/**
 * Admin Settings Class
 *
 * Handles all admin-related functionality including settings page,
 * options registration, and admin interface.
 *
 * @package Jo<PERSON>o_WC_Chatbot
 * @since 0.3.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class JoJo_WC_Chatbot_Admin_Settings
 */
class JoJo_WC_Chatbot_Admin_Settings {

    /**
     * Option group name
     */
    const OPT_GROUP = 'jojo_wc_chatbot';

    /**
     * Option keys
     */
    const OPT_API_KEY = 'jojo_wc_chatbot_api_key';
    const OPT_PUBLIC_TOKEN = 'jojo_wc_chatbot_public_token';
    const OPT_MODEL = 'jojo_wc_chatbot_model';
    const OPT_CUSTOM_BUTTON_IMAGE = 'jojo_wc_chatbot_custom_button_image';
    const OPT_BUTTON_TEXT = 'jojo_wc_chatbot_button_text';
    const OPT_BUTTON_BG_COLOR = 'jojo_wc_chatbot_button_bg_color';
    const OPT_BUTTON_TEXT_COLOR = 'jojo_wc_chatbot_button_text_color';
    const OPT_SHOW_ON_ALL_PAGES = 'jojo_wc_chatbot_show_on_all_pages';
    const OPT_REFUND_PAGE_URL = 'jojo_wc_chatbot_refund_page_url';
    const OPT_AVERAGE_DELIVERY_DAYS = 'jojo_wc_chatbot_average_delivery_days';
    const OPT_MAX_DELIVERY_DAYS = 'jojo_wc_chatbot_max_delivery_days';

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'register_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * Register the settings page in WordPress admin
     */
    public function register_settings_page() {
        add_options_page(
            __('JoJo WC Chatbot Settings', 'jojo-wc-chatbot'),
            __('JoJo Chatbot', 'jojo-wc-chatbot'),
            'manage_options',
            'jojo-wc-chatbot',
            array($this, 'settings_page_html')
        );
    }

    /**
     * Register all plugin settings
     */
    public function register_settings() {
        // Register settings
        register_setting(self::OPT_GROUP, self::OPT_API_KEY, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field'
        ));
        
        register_setting(self::OPT_GROUP, self::OPT_PUBLIC_TOKEN, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field'
        ));
        
        register_setting(self::OPT_GROUP, self::OPT_MODEL, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field'
        ));
        
        register_setting(self::OPT_GROUP, self::OPT_CUSTOM_BUTTON_IMAGE, array(
            'type' => 'string',
            'sanitize_callback' => 'esc_url_raw'
        ));
        
        register_setting(self::OPT_GROUP, self::OPT_BUTTON_TEXT, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field'
        ));

        register_setting(self::OPT_GROUP, self::OPT_BUTTON_BG_COLOR, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_hex_color'
        ));

        register_setting(self::OPT_GROUP, self::OPT_BUTTON_TEXT_COLOR, array(
            'type' => 'string',
            'sanitize_callback' => 'sanitize_hex_color'
        ));

        register_setting(self::OPT_GROUP, self::OPT_SHOW_ON_ALL_PAGES, array(
            'type' => 'boolean',
            'sanitize_callback' => 'rest_sanitize_boolean'
        ));

        register_setting(self::OPT_GROUP, self::OPT_REFUND_PAGE_URL, array(
            'type' => 'string',
            'sanitize_callback' => 'esc_url_raw'
        ));

        register_setting(self::OPT_GROUP, self::OPT_AVERAGE_DELIVERY_DAYS, array(
            'type' => 'integer',
            'sanitize_callback' => 'absint'
        ));

        register_setting(self::OPT_GROUP, self::OPT_MAX_DELIVERY_DAYS, array(
            'type' => 'integer',
            'sanitize_callback' => 'absint'
        ));

        // Add settings sections
        add_settings_section(
            'main',
            __('Impostazioni Principali', 'jojo-wc-chatbot'),
            array($this, 'main_section_callback'),
            'jojo-wc-chatbot'
        );

        add_settings_section(
            'appearance',
            __('Impostazioni Aspetto', 'jojo-wc-chatbot'),
            array($this, 'appearance_section_callback'),
            'jojo-wc-chatbot'
        );

        add_settings_section(
            'chatbot_behavior',
            __('Comportamento Chatbot', 'jojo-wc-chatbot'),
            array($this, 'chatbot_behavior_section_callback'),
            'jojo-wc-chatbot'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add all settings fields
     */
    private function add_settings_fields() {
        // Main settings fields
        add_settings_field(
            self::OPT_API_KEY,
            __('Chiave API OpenAI', 'jojo-wc-chatbot'),
            array($this, 'api_key_field_callback'),
            'jojo-wc-chatbot',
            'main'
        );

        add_settings_field(
            self::OPT_MODEL,
            __('Modello AI', 'jojo-wc-chatbot'),
            array($this, 'model_field_callback'),
            'jojo-wc-chatbot',
            'main'
        );

        add_settings_field(
            self::OPT_PUBLIC_TOKEN,
            __('Token Pubblico (Opzionale)', 'jojo-wc-chatbot'),
            array($this, 'public_token_field_callback'),
            'jojo-wc-chatbot',
            'main'
        );

        // Appearance settings fields
        add_settings_field(
            self::OPT_BUTTON_TEXT,
            __('Testo Pulsante', 'jojo-wc-chatbot'),
            array($this, 'button_text_field_callback'),
            'jojo-wc-chatbot',
            'appearance'
        );

        add_settings_field(
            self::OPT_CUSTOM_BUTTON_IMAGE,
            __('Immagine Pulsante Personalizzata', 'jojo-wc-chatbot'),
            array($this, 'custom_button_image_field_callback'),
            'jojo-wc-chatbot',
            'appearance'
        );

        add_settings_field(
            self::OPT_BUTTON_BG_COLOR,
            __('Colore Sfondo Pulsante', 'jojo-wc-chatbot'),
            array($this, 'button_bg_color_field_callback'),
            'jojo-wc-chatbot',
            'appearance'
        );

        add_settings_field(
            self::OPT_BUTTON_TEXT_COLOR,
            __('Colore Testo Pulsante', 'jojo-wc-chatbot'),
            array($this, 'button_text_color_field_callback'),
            'jojo-wc-chatbot',
            'appearance'
        );

        add_settings_field(
            self::OPT_SHOW_ON_ALL_PAGES,
            __('Mostra su Tutte le Pagine', 'jojo-wc-chatbot'),
            array($this, 'show_on_all_pages_field_callback'),
            'jojo-wc-chatbot',
            'appearance'
        );

        // Chatbot behavior settings fields
        add_settings_field(
            self::OPT_REFUND_PAGE_URL,
            __('Link Pagina Rimborsi', 'jojo-wc-chatbot'),
            array($this, 'refund_page_url_field_callback'),
            'jojo-wc-chatbot',
            'chatbot_behavior'
        );

        add_settings_field(
            self::OPT_AVERAGE_DELIVERY_DAYS,
            __('Tempi Medi di Consegna (giorni)', 'jojo-wc-chatbot'),
            array($this, 'average_delivery_days_field_callback'),
            'jojo-wc-chatbot',
            'chatbot_behavior'
        );

        add_settings_field(
            self::OPT_MAX_DELIVERY_DAYS,
            __('Tempo Massimo di Consegna (giorni)', 'jojo-wc-chatbot'),
            array($this, 'max_delivery_days_field_callback'),
            'jojo-wc-chatbot',
            'chatbot_behavior'
        );
    }

    /**
     * Main section callback
     */
    public function main_section_callback() {
        echo '<p>' . __('Configura le impostazioni API di OpenAI e il token di sicurezza opzionale.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Appearance section callback
     */
    public function appearance_section_callback() {
        echo '<p>' . __('Personalizza l\'aspetto del widget chatbot.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Chatbot behavior section callback
     */
    public function chatbot_behavior_section_callback() {
        echo '<p>' . __('Configura il comportamento del chatbot per rimborsi e tempi di consegna.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * API Key field callback
     */
    public function api_key_field_callback() {
        $value = esc_attr(get_option(self::OPT_API_KEY, ''));
        echo '<input type="password" name="' . self::OPT_API_KEY . '" value="' . $value . '" class="regular-text" />';
        echo '<p class="description">' . __('Consigliato: definisci OPENAI_API_KEY in wp-config.php. In alternativa, inseriscila qui.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Model field callback
     */
    public function model_field_callback() {
        $value = esc_attr(get_option(self::OPT_MODEL, 'gpt-4o-mini'));
        echo '<input type="text" name="' . self::OPT_MODEL . '" value="' . $value . '" class="regular-text" />';
        echo '<p class="description">' . __('Default consigliato: gpt-4o-mini. Compatibile con varianti recenti che supportano function calling.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Public token field callback
     */
    public function public_token_field_callback() {
        $value = esc_attr(get_option(self::OPT_PUBLIC_TOKEN, ''));
        echo '<input type="text" name="' . self::OPT_PUBLIC_TOKEN . '" value="' . $value . '" class="regular-text" />';
        echo '<p class="description">' . __('Se impostato, le chiamate a /jojo/v1/llm richiedono header X-JOJO-TOKEN uguale a questo valore.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Button text field callback
     */
    public function button_text_field_callback() {
        $value = esc_attr(get_option(self::OPT_BUTTON_TEXT, 'Chat'));
        echo '<input type="text" name="' . self::OPT_BUTTON_TEXT . '" value="' . $value . '" class="regular-text" placeholder="Chat" />';
        echo '<p class="description">' . __('Testo da visualizzare sul pulsante chat. Lascia vuoto per usare il default.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Custom button image field callback
     */
    public function custom_button_image_field_callback() {
        $value = esc_url(get_option(self::OPT_CUSTOM_BUTTON_IMAGE, ''));
        echo '<input type="url" name="' . self::OPT_CUSTOM_BUTTON_IMAGE . '" value="' . $value . '" class="regular-text" id="custom-button-image-url" />';
        echo '<button type="button" class="button" id="upload-button-image">' . __('Carica Immagine', 'jojo-wc-chatbot') . '</button>';
        echo '<p class="description">' . __('Carica un\'immagine personalizzata per sostituire il pulsante chat predefinito. Dimensione consigliata: 50x50px.', 'jojo-wc-chatbot') . '</p>';

        if ($value) {
            echo '<div class="custom-button-preview" style="margin-top: 10px;">';
            echo '<img src="' . $value . '" alt="' . __('Anteprima Pulsante', 'jojo-wc-chatbot') . '" style="max-width: 35px; max-height: 35px; border: 1px solid #ddd; border-radius: 50%;" />';
            echo '</div>';
        }
    }

    /**
     * Button background color field callback
     */
    public function button_bg_color_field_callback() {
        $value = sanitize_hex_color(get_option(self::OPT_BUTTON_BG_COLOR, '#0170b8'));
        echo '<input type="color" name="' . self::OPT_BUTTON_BG_COLOR . '" value="' . $value . '" class="color-picker" />';
        echo '<p class="description">' . __('Colore di sfondo del pulsante chat. Default: #0170b8', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Button text color field callback
     */
    public function button_text_color_field_callback() {
        $value = sanitize_hex_color(get_option(self::OPT_BUTTON_TEXT_COLOR, '#ffffff'));
        echo '<input type="color" name="' . self::OPT_BUTTON_TEXT_COLOR . '" value="' . $value . '" class="color-picker" />';
        echo '<p class="description">' . __('Colore del testo del pulsante chat. Default: #ffffff', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Show on all pages field callback
     */
    public function show_on_all_pages_field_callback() {
        $value = get_option(self::OPT_SHOW_ON_ALL_PAGES, false);
        echo '<input type="checkbox" name="' . self::OPT_SHOW_ON_ALL_PAGES . '" value="1" ' . checked(1, $value, false) . ' />';
        echo '<p class="description">' . __('Se attivato, il widget chat apparirà automaticamente su tutte le pagine del sito (senza bisogno dello shortcode).', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Refund page URL field callback
     */
    public function refund_page_url_field_callback() {
        $value = esc_url(get_option(self::OPT_REFUND_PAGE_URL, ''));
        echo '<input type="url" name="' . self::OPT_REFUND_PAGE_URL . '" value="' . $value . '" class="regular-text" placeholder="https://example.com/rimborsi" />';
        echo '<p class="description">' . __('URL della pagina con le informazioni sui rimborsi. Il chatbot indirizzerà qui gli utenti che chiedono informazioni sui rimborsi.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Average delivery days field callback
     */
    public function average_delivery_days_field_callback() {
        $value = absint(get_option(self::OPT_AVERAGE_DELIVERY_DAYS, 3));
        echo '<input type="number" name="' . self::OPT_AVERAGE_DELIVERY_DAYS . '" value="' . $value . '" min="1" max="30" class="small-text" />';
        echo '<p class="description">' . __('Numero medio di giorni per la consegna di un prodotto. Utilizzato quando i clienti chiedono i tempi di consegna generici.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Max delivery days field callback
     */
    public function max_delivery_days_field_callback() {
        $value = absint(get_option(self::OPT_MAX_DELIVERY_DAYS, 7));
        echo '<input type="number" name="' . self::OPT_MAX_DELIVERY_DAYS . '" value="' . $value . '" min="1" max="60" class="small-text" />';
        echo '<p class="description">' . __('Numero massimo di giorni per la consegna. Utilizzato per calcolare la data di consegna prevista partendo dalla data di creazione dell\'ordine.', 'jojo-wc-chatbot') . '</p>';
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_jojo-wc-chatbot') {
            return;
        }

        wp_enqueue_media();
        wp_enqueue_script(
            'jojo-wc-chatbot-admin',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/admin.js',
            array('jquery'),
            '0.3.0',
            true
        );
    }

    /**
     * Render the settings page HTML
     */
    public function settings_page_html() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Handle form submission messages
        if (isset($_GET['settings-updated'])) {
            add_settings_error('jojo_wc_chatbot_messages', 'jojo_wc_chatbot_message', __('Impostazioni Salvate', 'jojo-wc-chatbot'), 'updated');
        }

        settings_errors('jojo_wc_chatbot_messages');
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields(self::OPT_GROUP);
                do_settings_sections('jojo-wc-chatbot');
                submit_button(__('Salva Impostazioni', 'jojo-wc-chatbot'));
                ?>
            </form>

            <hr/>

            <div class="jojo-usage-guide">
                <h2><?php _e('Guida Rapida', 'jojo-wc-chatbot'); ?></h2>
                <ol>
                    <li><?php _e('Inserisci la tua chiave API OpenAI e salva le impostazioni.', 'jojo-wc-chatbot'); ?></li>
                    <li><?php _e('Aggiungi lo shortcode <code>[jojo_wc_chat]</code> in qualsiasi pagina (es. /supporto o footer).', 'jojo-wc-chatbot'); ?></li>
                    <li><?php _e('Testa chiedendo: "Stato ordine 1234" o "Cerco cuffie bluetooth sotto 50€"', 'jojo-wc-chatbot'); ?></li>
                </ol>

                <h3><?php _e('Opzioni Shortcode', 'jojo-wc-chatbot'); ?></h3>
                <p><?php _e('Puoi personalizzare il chatbot con questi attributi shortcode:', 'jojo-wc-chatbot'); ?></p>
                <ul>
                    <li><code>title</code> - <?php _e('Titolo finestra chat', 'jojo-wc-chatbot'); ?></li>
                    <li><code>subtitle</code> - <?php _e('Sottotitolo finestra chat', 'jojo-wc-chatbot'); ?></li>
                    <li><code>placeholder</code> - <?php _e('Testo placeholder campo input', 'jojo-wc-chatbot'); ?></li>
                </ul>
                <p><strong><?php _e('Esempio:', 'jojo-wc-chatbot'); ?></strong> <code>[jojo_wc_chat title="Hai bisogno di aiuto?" subtitle="Chiedi informazioni su ordini o prodotti"]</code></p>
            </div>
        </div>
        <?php
    }

    /**
     * Get option value with fallback
     */
    public static function get_option($option_name, $default = '') {
        return get_option($option_name, $default);
    }
}
