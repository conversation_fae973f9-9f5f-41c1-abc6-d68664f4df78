<?php
/**
 * OpenAI Integration Class
 *
 * Handles all OpenAI API interactions and tool implementations
 * for order status checking and product recommendations.
 *
 * @package Jo<PERSON>o_WC_Chatbot
 * @since 0.3.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class JoJo_WC_Chatbot_OpenAI_Integration
 */
class JoJo_WC_Chatbot_OpenAI_Integration {

    /**
     * Process LLM request with function calling or intent routing
     *
     * @param string $message User message
     * @param bool $use_intent_router Whether to use intent router (default: false for backward compatibility)
     * @return array Response data
     */
    public function process_llm_request($message, $use_intent_router = false) {
        // If intent router is requested, use it for simple intents
        if ($use_intent_router) {
            require_once JOJO_WC_CHATBOT_INCLUDES_DIR . 'utils/class-intent-router.php';
            $router = new JoJo_WC_Chatbot_Intent_Router();
            return $router->route_message($message);
        }
        $messages = array(
            array(
                "role" => "system",
                "content" => "Sei un assistente per e-commerce WooCommerce. Compiti: (1) Stato ordine. (2) Consigli prodotto. (3) Tempi di consegna generici. (4) Stima consegna ordine specifico. (5) Informazioni rimborsi. Devi SEMPRE usare i tools definiti, MAI inventare dati. Per stato ordine e stima consegna richiedi almeno email o cognome. Se mancano campi, chiedili in modo conciso. Per i consigli prodotto, elenca SOLO nome e prezzo, MAI includere URL o link alle immagini nel testo. Rispondi in italiano, tono cortese e sintetico."
            ),
            array("role" => "user", "content" => $message)
        );

        // First call: decide if using a tool and with which args
        $first_response = $this->openai_chat(array(
            "messages" => $messages,
            "tools" => $this->get_function_definitions(),
            "tool_choice" => "auto",
            "temperature" => 0.2
        ));

        if (!empty($first_response['error'])) {
            return array("reply" => $first_response['error']);
        }

        $choice = $first_response['choices'][0]['message'] ?? array();
        $tool_calls = $choice['tool_calls'] ?? array();

        if ($tool_calls) {
            return $this->handle_tool_calls($messages, $choice, $tool_calls);
        }

        // No tool: probably asking for missing info/clarification
        $text = $choice['content'] ?? '';
        return array("reply" => trim($text ?: __("Can you rephrase the question?", 'jojo-wc-chatbot')));
    }

    /**
     * Handle tool calls and generate final response
     *
     * @param array $messages Original messages
     * @param array $choice Assistant choice with tool calls
     * @param array $tool_calls Tool calls to execute
     * @return array Response data
     */
    private function handle_tool_calls($messages, $choice, $tool_calls) {
        $tool_messages = array();
        $recommended_products = array();

        foreach ($tool_calls as $call) {
            $name = $call['function']['name'] ?? '';
            $args = json_decode($call['function']['arguments'] ?? '{}', true) ?: array();
            $result = null;

            switch ($name) {
                case 'get_order_status':
                    $result = $this->get_order_status($args);
                    break;
                case 'recommend_products':
                    $result = $this->recommend_products($args);
                    if (!empty($result['results']) && is_array($result['results'])) {
                        $recommended_products = $result['results'];
                    }
                    break;
                case 'get_delivery_info':
                    $result = $this->get_delivery_info($args);
                    break;
                case 'get_delivery_estimate':
                    $result = $this->get_delivery_estimate($args);
                    break;
                case 'get_refund_info':
                    $result = $this->get_refund_info($args);
                    break;
                default:
                    $result = array("error" => __("Unknown tool: ", 'jojo-wc-chatbot') . $name);
            }

            $tool_messages[] = array(
                "role" => "tool",
                "tool_call_id" => $call['id'],
                "name" => $name,
                "content" => wp_json_encode($result, JSON_UNESCAPED_UNICODE)
            );
        }

        // Second call: provide tool results and get final response
        $second_messages = array_merge(
            $messages,
            array($choice),     // assistant with tool_calls
            $tool_messages      // tool results
        );

        $final_response = $this->openai_chat(array(
            "messages" => $second_messages,
            "temperature" => 0.2
        ));

        if (!empty($final_response['error'])) {
            return array("reply" => $final_response['error']);
        }

        $text = $final_response['choices'][0]['message']['content'] ?? '';
        return array(
            "reply" => trim($text ?: "Non ho capito, puoi riformulare?"),
            "products" => $recommended_products
        );
    }

    /**
     * Get function definitions for OpenAI tools
     *
     * @return array Function definitions
     */
    private function get_function_definitions() {
        return array(
            array(
                "type" => "function",
                "function" => array(
                    "name" => "get_order_status",
                    "description" => "Recupera lo stato dell'ordine da WooCommerce. Richiede order_id e (email o cognome).",
                    "parameters" => array(
                        "type" => "object",
                        "properties" => array(
                            "order_id" => array("type" => "integer"),
                            "email" => array("type" => "string"),
                            "last_name" => array("type" => "string")
                        ),
                        "required" => array("order_id"),
                        "additionalProperties" => false
                    )
                )
            ),
            array(
                "type" => "function",
                "function" => array(
                    "name" => "recommend_products",
                    "description" => "Suggerisci prodotti disponibili filtrando per testo/categoria/budget.",
                    "parameters" => array(
                        "type" => "object",
                        "properties" => array(
                            "search" => array("type" => "string"),
                            "category" => array("type" => "string"),
                            "min_price" => array("type" => "number"),
                            "max_price" => array("type" => "number"),
                            "limit" => array("type" => "integer", "default" => 5)
                        ),
                        "additionalProperties" => false
                    )
                )
            ),
            array(
                "type" => "function",
                "function" => array(
                    "name" => "get_delivery_info",
                    "description" => "Fornisce informazioni sui tempi di consegna generici configurati dall'amministratore.",
                    "parameters" => array(
                        "type" => "object",
                        "properties" => array(),
                        "additionalProperties" => false
                    )
                )
            ),
            array(
                "type" => "function",
                "function" => array(
                    "name" => "get_delivery_estimate",
                    "description" => "Calcola la data di consegna prevista per un ordine specifico basata sulla data di creazione e i giorni massimi configurati.",
                    "parameters" => array(
                        "type" => "object",
                        "properties" => array(
                            "order_id" => array("type" => "integer"),
                            "email" => array("type" => "string"),
                            "last_name" => array("type" => "string")
                        ),
                        "required" => array("order_id"),
                        "additionalProperties" => false
                    )
                )
            ),
            array(
                "type" => "function",
                "function" => array(
                    "name" => "get_refund_info",
                    "description" => "Fornisce informazioni sui rimborsi e il link alla pagina dedicata configurata dall'amministratore.",
                    "parameters" => array(
                        "type" => "object",
                        "properties" => array(),
                        "additionalProperties" => false
                    )
                )
            )
        );
    }

    /**
     * Get order status implementation
     *
     * @param array $args Function arguments
     * @return array Order status data
     */
    private function get_order_status($args) {
        $order_id = intval($args['order_id'] ?? 0);
        $email = sanitize_email($args['email'] ?? '');
        $last_name = sanitize_text_field($args['last_name'] ?? '');

        if (!$order_id) {
            return array("error" => "ID ordine mancante.");
        }

        if (!class_exists('WC_Order')) {
            return array("error" => "WooCommerce non attivo.");
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            return array("error" => "Ordine non trovato.");
        }

        $billing_email = $order->get_billing_email();
        $billing_last = $order->get_billing_last_name();

        $email_ok = $email ? (mb_strtolower($email) === mb_strtolower($billing_email)) : false;
        $last_ok = $last_name ? (mb_strtolower($last_name) === mb_strtolower($billing_last)) : false;

        if (!$email_ok && !$last_ok) {
            return array("error" => "Verifica fallita: email o cognome non corrispondono.");
        }

        $status = wc_get_order_status_name($order->get_status());
        $total = wp_strip_all_tags(wc_price($order->get_total()));
        $created = $order->get_date_created() ? $order->get_date_created()->date_i18n(get_option('date_format')) : '';
        $tracking = $this->get_tracking_info($order);

        return array(
            "ok" => true,
            "order_id" => $order->get_id(),
            "status" => $status,
            "total" => $total,
            "created" => $created,
            "tracking" => $tracking
        );
    }

    /**
     * Get tracking information from order
     *
     * @param WC_Order $order Order object
     * @return array Tracking information
     */
    private function get_tracking_info($order) {
        $meta_keys = array('_tracking_number', '_tracking_provider', '_tracking_url', 'tracking_number', 'tracking_url');
        $tracking_info = array();

        foreach ($meta_keys as $key) {
            $value = $order->get_meta($key);
            if (!empty($value)) {
                $tracking_info[$key] = $value;
            }
        }

        // Check order notes for tracking links
        $notes = wc_get_order_notes(array('order_id' => $order->get_id()));
        foreach ($notes as $note) {
            if (preg_match('#https?://[^\s]+#', $note->content, $matches)) {
                $tracking_info['note_link'] = $matches[0];
                break;
            }
        }

        return $tracking_info;
    }

    /**
     * Recommend products implementation
     *
     * @param array $args Function arguments
     * @return array Product recommendations
     */
    private function recommend_products($args) {
        $query_args = array(
            'status' => 'publish',
            'stock_status' => 'instock',
            'limit' => max(1, min(12, intval($args['limit'] ?? 5))),
            'orderby' => 'date',
            'order' => 'DESC',
        );

        if (!empty($args['category'])) {
            $query_args['category'] = array(sanitize_title($args['category']));
        }

        if (!empty($args['search'])) {
            $query_args['s'] = sanitize_text_field($args['search']);
        }

        $meta_query = array();
        if (!empty($args['min_price'])) {
            $meta_query[] = array(
                'key' => '_price',
                'value' => floatval($args['min_price']),
                'compare' => '>=',
                'type' => 'NUMERIC'
            );
        }

        if (!empty($args['max_price'])) {
            $meta_query[] = array(
                'key' => '_price',
                'value' => floatval($args['max_price']),
                'compare' => '<=',
                'type' => 'NUMERIC'
            );
        }

        if ($meta_query) {
            $query_args['meta_query'] = $meta_query;
        }

        $products = wc_get_products($query_args);
        $results = array();

        foreach ($products as $product) {
            $results[] = array(
                "id" => $product->get_id(),
                "name" => $product->get_name(),
                "price" => wc_price($product->get_price()),
                "url" => get_permalink($product->get_id()),
                "image" => get_the_post_thumbnail_url($product->get_id(), 'medium') ?: wc_placeholder_img_src(),
                "short_description" => wp_strip_all_tags($product->get_short_description()),
            );
        }

        return array("ok" => true, "results" => $results);
    }

    /**
     * Get delivery information implementation
     *
     * @param array $args Function arguments (unused)
     * @return array Delivery information
     */
    private function get_delivery_info($args) {
        $average_days = absint(JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_AVERAGE_DELIVERY_DAYS,
            3
        ));

        return array(
            "ok" => true,
            "average_delivery_days" => $average_days,
            "message" => sprintf(
                __("I tempi medi di consegna sono di %d giorni lavorativi dalla conferma dell'ordine.", 'jojo-wc-chatbot'),
                $average_days
            )
        );
    }

    /**
     * Get delivery estimate for specific order implementation
     *
     * @param array $args Function arguments
     * @return array Delivery estimate data
     */
    private function get_delivery_estimate($args) {
        $order_id = intval($args['order_id'] ?? 0);
        $email = sanitize_email($args['email'] ?? '');
        $last_name = sanitize_text_field($args['last_name'] ?? '');

        if (!$order_id) {
            return array("error" => "ID ordine mancante.");
        }

        if (!class_exists('WC_Order')) {
            return array("error" => "WooCommerce non attivo.");
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            return array("error" => "Ordine non trovato.");
        }

        // Verify order ownership if email or last_name provided
        if ($email || $last_name) {
            $billing_email = $order->get_billing_email();
            $billing_last = $order->get_billing_last_name();

            $email_ok = $email ? (mb_strtolower($email) === mb_strtolower($billing_email)) : false;
            $last_ok = $last_name ? (mb_strtolower($last_name) === mb_strtolower($billing_last)) : false;

            if (!$email_ok && !$last_ok) {
                return array("error" => "Verifica fallita: email o cognome non corrispondono.");
            }
        }

        $max_days = absint(JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_MAX_DELIVERY_DAYS,
            7
        ));

        $created_date = $order->get_date_created();
        if (!$created_date) {
            return array("error" => "Data di creazione ordine non disponibile.");
        }

        // Calculate estimated delivery date
        $estimated_date = clone $created_date;
        $estimated_date->modify("+{$max_days} days");

        return array(
            "ok" => true,
            "order_id" => $order->get_id(),
            "created_date" => $created_date->date_i18n(get_option('date_format')),
            "estimated_delivery_date" => $estimated_date->date_i18n(get_option('date_format')),
            "max_delivery_days" => $max_days,
            "message" => sprintf(
                __("L'ordine #%d è stato creato il %s. La consegna è prevista entro il %s (%d giorni lavorativi).", 'jojo-wc-chatbot'),
                $order->get_id(),
                $created_date->date_i18n(get_option('date_format')),
                $estimated_date->date_i18n(get_option('date_format')),
                $max_days
            )
        );
    }

    /**
     * Get refund information implementation
     *
     * @param array $args Function arguments (unused)
     * @return array Refund information
     */
    private function get_refund_info($args) {
        $refund_url = esc_url(JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_REFUND_PAGE_URL,
            ''
        ));

        if (empty($refund_url)) {
            return array(
                "ok" => true,
                "message" => __("Per informazioni sui rimborsi, contatta il nostro servizio clienti.", 'jojo-wc-chatbot')
            );
        }

        return array(
            "ok" => true,
            "refund_page_url" => $refund_url,
            "message" => sprintf(
                __("Per informazioni dettagliate sui rimborsi e le procedure di reso, visita la nostra pagina dedicata: %s", 'jojo-wc-chatbot'),
                $refund_url
            )
        );
    }

    /**
     * Make OpenAI Chat Completions API call
     *
     * @param array $payload Request payload
     * @return array API response
     */
    public function openai_chat($payload) {
        $api_key = $this->get_api_key();
        if (!$api_key) {
            return array("error" => __("Missing API key. Go to Settings > JoJo Chatbot.", 'jojo-wc-chatbot'));
        }

        if (empty($payload['model'])) {
            $payload['model'] = JoJo_WC_Chatbot_Admin_Settings::get_option(
                JoJo_WC_Chatbot_Admin_Settings::OPT_MODEL,
                'gpt-4o-mini'
            );
        }

        if (!isset($payload['temperature'])) {
            $payload['temperature'] = 0.2;
        }

        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode($payload, JSON_UNESCAPED_UNICODE),
            'timeout' => 25,
        );

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', $args);

        if (is_wp_error($response)) {
            return array("error" => __("API error (connection): ", 'jojo-wc-chatbot') . $response->get_error_message());
        }

        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        if ($code >= 400) {
            return array("error" => sprintf(__("API error (%d): %s", 'jojo-wc-chatbot'), $code, substr($body, 0, 350)));
        }

        return json_decode($body, true);
    }

    /**
     * Get API key from settings or constant
     *
     * @return string API key
     */
    private function get_api_key() {
        $api_key = JoJo_WC_Chatbot_Admin_Settings::get_option(JoJo_WC_Chatbot_Admin_Settings::OPT_API_KEY);
        
        if (!$api_key && defined('OPENAI_API_KEY')) {
            $api_key = OPENAI_API_KEY;
        }

        return $api_key;
    }

    /**
     * Test API connection
     *
     * @return array Test results
     */
    public function test_api_connection() {
        $api_key = $this->get_api_key();
        if (!$api_key) {
            return array(
                'ok' => false,
                'error' => __('API key missing. Set the key.', 'jojo-wc-chatbot')
            );
        }

        $args = array(
            'headers' => array('Authorization' => 'Bearer ' . $api_key),
            'timeout' => 15,
        );

        $response = wp_remote_get('https://api.openai.com/v1/models', $args);

        if (is_wp_error($response)) {
            return array(
                'ok' => false,
                'wp_error' => $response->get_error_message()
            );
        }

        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        return array(
            'ok' => $code < 400,
            'status' => $code,
            'body_excerpt' => substr($body, 0, 200)
        );
    }
}
