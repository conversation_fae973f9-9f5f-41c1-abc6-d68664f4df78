<?php
/**
 * Plugin Name: WooCommerce Order Report (Ottimizzato)
 * Description: Plugin ottimizzato per generare report dei prodotti acquistati con query efficienti e best practices WordPress/WooCommerce.
 * Version: 2.0
 * Author: <PERSON>
 * Text Domain: wc-order-report
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

class WC_Order_Report_Plugin {

    private $results_per_page = 100;
    private $cache_expiry = 3600; // 1 ora

    public function __construct() {
        // Aggiunge la voce di menu nell'area admin
        add_action( 'admin_menu', array( $this, 'add_menu_page' ) );
        // Enqueue degli script e stili solo nella pagina del plugin
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
        // Hook per AJAX se necessario
        add_action( 'wp_ajax_wc_order_report_data', array( $this, 'ajax_get_report_data' ) );
        // Attiva gli hook per la gestione della cache
        $this->setup_cache_hooks();
    }

    /**
     * Enqueue degli script e stili necessari per la pagina del report.
     * Includiamo DataTables, jsPDF e jsPDF AutoTable.
     */
    public function enqueue_admin_scripts( $hook ) {
        // Controlla se siamo nella pagina del plugin (la slug creata in add_menu_page)
        if ( 'toplevel_page_wc-order-report' !== $hook ) {
            return;
        }


        // Carica Bootstrap CSS
        wp_enqueue_style('bootstrap-css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css');
        wp_enqueue_style('order-report-css', plugin_dir_url( __FILE__ ) . 'wc-order-report.css');
        // Carica Bootstrap JS con dipendenza da jQuery
        wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js', array('jquery'), null, true);

        // Enqueue Select2 CSS e JS per l'autocomplete
        wp_enqueue_style('select2-css', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
        wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), '4.1.0', true);

        // Enqueue DataTables CSS e JS da CDN
        wp_enqueue_style( 'datatables-css', 'https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css' );
        wp_enqueue_script( 'datatables-js', 'https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js', array( 'jquery' ), '1.13.4', true );

        // Enqueue jsPDF e jsPDF AutoTable da CDN
        wp_enqueue_script( 'jspdf', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', array(), '2.5.1', true );
        wp_enqueue_script( 'jspdf-autotable', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.23/jspdf.plugin.autotable.min.js', array( 'jspdf' ), '3.5.23', true );

        // Enqueue il nostro script custom per DataTables e PDF export.
        wp_enqueue_script( 'wc-order-report-custom-js', plugin_dir_url( __FILE__ ) . 'wc-order-report.js', array( 'jquery', 'datatables-js', 'jspdf', 'jspdf-autotable', 'select2-js' ), '1.0', true );
    }

    /**
     * Aggiunge la pagina del report nel menu di amministrazione.
     */
    public function add_menu_page() {
        add_menu_page(
            __( 'Report Ordini WooCommerce', 'wc-order-report' ),
            __( 'Report Ordini', 'wc-order-report' ),
            'manage_options',
            'wc-order-report',
            array( $this, 'render_report_page' ),
            'dashicons-chart-bar',
            6
        );
    }



    /**
     * Visualizza la pagina del report con il form di ricerca, la tabella dei risultati
     * e il pulsante per l'esportazione in PDF.
     */
    public function render_report_page() {
        ?>
        <div class="container mt-4">
            <h1 class="mb-4"><?php _e( 'Report dei Prodotti Acquistati', 'wc-order-report' ); ?></h1>
            <form method="GET" class="p-4 border rounded bg-light">
                <!-- Necessario per mantenere la pagina corrente -->
                <input type="hidden" name="page" value="wc-order-report" />
                <?php wp_nonce_field( 'wc_order_report_nonce', 'wc_order_report_nonce' ); ?>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Data Iniziale:</label>
                        <input type="date" class="form-control" name="start_date" id="start_date" value="<?php echo ( isset( $_GET['start_date'] ) && !is_array($_GET['start_date']) ) ? esc_attr( $_GET['start_date'] ) : ''; ?>" />
                    </div>         
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">Data Finale:</label>
                        <input type="date" class="form-control" name="end_date" id="end_date" value="<?php echo ( isset( $_GET['end_date'] ) && !is_array($_GET['end_date']) ) ? esc_attr( $_GET['end_date'] ) : ''; ?>" />
                    </div>   
                </div>        
 
                <!-- Selezione Status Ordine -->
                <div class="mb-3">
                    <label class="form-label">Stato Ordine:</label>
                    <select class="form-control order-status-select" name="order_statuses[]" multiple="multiple" style="width: 100%;">
                        <?php
                        $order_statuses = wc_get_order_statuses();
                        $selected_statuses = isset($_GET['order_statuses']) ? (array)$_GET['order_statuses'] : array();
                        
                        if (!empty($order_statuses)) {
                            foreach ($order_statuses as $status_key => $status_name) {
                                $selected = in_array($status_key, $selected_statuses) ? 'selected="selected"' : '';
                                echo '<option value="' . esc_attr($status_key) . '" ' . $selected . '>' . esc_html($status_name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div> 
                
                

                    <!-- Categorie Prodotto -->
                <div class="mb-3">
                    <label class="form-label">Categorie Prodotti:</label>
                    <select class="form-control product-category-select" name="product-category[]" multiple="multiple" style="width: 100%;">
                        <?php
                        $product_categories = get_terms(array(
                            'taxonomy'   => 'product_cat',
                            'hide_empty' => false,
                            'orderby'    => 'name',
                            'order'      => 'ASC'
                        ));
                        
                        $selected_categories = isset($_GET['product-category']) ? (array)$_GET['product-category'] : array();
                        
                        if (!empty($product_categories) && !is_wp_error($product_categories)) {
                            foreach ($product_categories as $category) {
                                $selected = in_array($category->term_id, $selected_categories) ? 'selected="selected"' : '';
                                $padding = '';
                                
                                // Aggiunge indentazione per le sottocategorie
                                if ($category->parent > 0) {
                                    $padding = '&nbsp;&nbsp;&nbsp;';
                                }
                                
                                echo '<option value="' . esc_attr($category->term_id) . '" ' . $selected . '>' 
                                    . $padding . esc_html($category->name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <table>   
                    <!-- Ordinamento server-side (opzionale) -->
                    <tr valign="top">
                        <td ><?php _e( 'Ordina per', 'wc-order-report' ); ?></td>
                    </tr>                        
                    <tr>                        
                        <td>
                            <select name="sortby">
                                <option value="name" <?php echo ( isset( $_GET['sortby'] ) && $_GET['sortby'] == 'name' ) ? 'selected' : ''; ?>><?php _e( 'Nome Prodotto', 'wc-order-report' ); ?></option>
                                <option value="quantity" <?php echo ( isset( $_GET['sortby'] ) && $_GET['sortby'] == 'quantity' ) ? 'selected' : ''; ?>><?php _e( 'Quantità', 'wc-order-report' ); ?></option>
                            </select>
                            <select name="order">
                                <option value="asc" <?php echo ( isset( $_GET['order'] ) && $_GET['order'] == 'asc' ) ? 'selected' : ''; ?>><?php _e( 'Ascendente', 'wc-order-report' ); ?></option>
                                <option value="desc" <?php echo ( isset( $_GET['order'] ) && $_GET['order'] == 'desc' ) ? 'selected' : ''; ?>><?php _e( 'Discendente', 'wc-order-report' ); ?></option>
                            </select>
                        </td>
                    </tr>
                </table>
                <?php submit_button( __( 'Genera Report', 'wc-order-report' ) ); ?>
            </form>
            
            <?php
            // Se il form è stato compilato (almeno le date sono presenti) eseguo il report
            if ( isset( $_GET['start_date'] ) && isset( $_GET['end_date'] ) ) {
                // Verifica nonce per sicurezza
                if ( ! isset( $_GET['wc_order_report_nonce'] ) || ! wp_verify_nonce( $_GET['wc_order_report_nonce'], 'wc_order_report_nonce' ) ) {
                    echo '<div class="notice notice-error"><p>' . __( 'Errore di sicurezza. Riprova.', 'wc-order-report' ) . '</p></div>';
                } else {
                    $this->process_report();
                }
            }
            ?>
        </div>
        <?php
    }

    /**
     * Elabora i dati in base ai filtri impostati e visualizza la tabella dei risultati.
     * Versione ottimizzata con query SQL dirette.
     */
    public function process_report() {
        // Validazione e sanitizzazione input
        $filters = $this->sanitize_and_validate_inputs();

        // Controlla cache
        $cache_key = 'wc_order_report_' . md5( serialize( $filters ) );
        $products_report = get_transient( $cache_key );

        if ( false === $products_report ) {
            // Genera il report con query ottimizzata
            $products_report = $this->get_optimized_report_data( $filters );

            // Salva in cache per 1 ora
            set_transient( $cache_key, $products_report, $this->cache_expiry );
        }

        // Applica ordinamento
        $products_report = $this->apply_sorting( $products_report, $filters['sortby'], $filters['order'] );

        // Applica paginazione
        $page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
        $total_items = count( $products_report );
        $total_pages = ceil( $total_items / $this->results_per_page );
        $offset = ( $page - 1 ) * $this->results_per_page;
        $paged_results = array_slice( $products_report, $offset, $this->results_per_page );

        $this->render_report_results( $paged_results, $page, $total_pages, $total_items );
    }

    /**
     * Sanitizza e valida gli input del form
     */
    private function sanitize_and_validate_inputs() {
        $start_date = isset( $_GET['start_date'] ) && ! is_array( $_GET['start_date'] )
            ? sanitize_text_field( $_GET['start_date'] ) : '';
        $end_date = isset( $_GET['end_date'] ) && ! is_array( $_GET['end_date'] )
            ? sanitize_text_field( $_GET['end_date'] ) : '';

        // Validazione date
        if ( ! empty( $start_date ) && ! $this->is_valid_date( $start_date ) ) {
            $start_date = '';
        }
        if ( ! empty( $end_date ) && ! $this->is_valid_date( $end_date ) ) {
            $end_date = '';
        }

        $order_statuses = isset( $_GET['order_statuses'] )
            ? array_map( 'sanitize_text_field', (array) $_GET['order_statuses'] ) : array();
        $product_categories = isset( $_GET['product-category'] )
            ? array_map( 'intval', (array) $_GET['product-category'] ) : array();

        $sortby = isset( $_GET['sortby'] ) ? sanitize_text_field( $_GET['sortby'] ) : 'name';
        $order = isset( $_GET['order'] ) ? sanitize_text_field( $_GET['order'] ) : 'asc';

        // Validazione sortby e order
        $sortby = in_array( $sortby, array( 'name', 'quantity' ) ) ? $sortby : 'name';
        $order = in_array( $order, array( 'asc', 'desc' ) ) ? $order : 'asc';

        return array(
            'start_date' => $start_date,
            'end_date' => $end_date,
            'order_statuses' => $order_statuses,
            'product_categories' => $product_categories,
            'sortby' => $sortby,
            'order' => $order
        );
    }

    /**
     * Verifica se una data è valida
     */
    private function is_valid_date( $date ) {
        $d = DateTime::createFromFormat( 'Y-m-d', $date );
        return $d && $d->format( 'Y-m-d' ) === $date;
    }

    /**
     * Ottiene i dati del report con query SQL ottimizzata
     */
    private function get_optimized_report_data( $filters ) {
        global $wpdb;

        // Costruisce la query SQL ottimizzata
        $sql = $this->build_optimized_query( $filters );

        // Esegue la query
        $results = $wpdb->get_results( $sql, ARRAY_A );

        if ( $wpdb->last_error ) {
            error_log( 'WC Order Report SQL Error: ' . $wpdb->last_error );
            return array();
        }

        // Processa i risultati
        $products_report = array();
        foreach ( $results as $row ) {
            $product_id = intval( $row['product_id'] );
            $products_report[ $product_id ] = array(
                'name' => $row['product_name'],
                'quantity' => intval( $row['total_quantity'] )
            );
        }

        return $products_report;
    }

    /**
     * Costruisce la query SQL ottimizzata
     */
    private function build_optimized_query( $filters ) {
        global $wpdb;

        $sql = "
            SELECT
                oi.order_item_name as product_name,
                oim_product.meta_value as product_id,
                SUM(CAST(oim_qty.meta_value AS UNSIGNED)) as total_quantity
            FROM {$wpdb->prefix}woocommerce_order_items oi
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product
                ON oi.order_item_id = oim_product.order_item_id
                AND oim_product.meta_key = '_product_id'
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty
                ON oi.order_item_id = oim_qty.order_item_id
                AND oim_qty.meta_key = '_qty'
            INNER JOIN {$wpdb->posts} p
                ON oi.order_id = p.ID
                AND p.post_type = 'shop_order'
        ";

        // Aggiunge JOIN per le categorie se necessario
        if ( ! empty( $filters['product_categories'] ) ) {
            $sql .= "
                INNER JOIN {$wpdb->term_relationships} tr
                    ON CAST(oim_product.meta_value AS UNSIGNED) = tr.object_id
                INNER JOIN {$wpdb->term_taxonomy} tt
                    ON tr.term_taxonomy_id = tt.term_taxonomy_id
                    AND tt.taxonomy = 'product_cat'
            ";
        }

        $where_conditions = array( "p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')" );

        // Filtro date
        if ( ! empty( $filters['start_date'] ) ) {
            $where_conditions[] = $wpdb->prepare( "p.post_date >= %s", $filters['start_date'] . ' 00:00:00' );
        }
        if ( ! empty( $filters['end_date'] ) ) {
            $where_conditions[] = $wpdb->prepare( "p.post_date <= %s", $filters['end_date'] . ' 23:59:59' );
        }

        // Filtro status ordini
        if ( ! empty( $filters['order_statuses'] ) ) {
            $status_placeholders = implode( ',', array_fill( 0, count( $filters['order_statuses'] ), '%s' ) );
            $where_conditions[] = $wpdb->prepare( "p.post_status IN ($status_placeholders)", $filters['order_statuses'] );
        }

        // Filtro categorie
        if ( ! empty( $filters['product_categories'] ) ) {
            $cat_placeholders = implode( ',', array_fill( 0, count( $filters['product_categories'] ), '%d' ) );
            $where_conditions[] = $wpdb->prepare( "tt.term_id IN ($cat_placeholders)", $filters['product_categories'] );
        }

        $sql .= " WHERE " . implode( ' AND ', $where_conditions );
        $sql .= " GROUP BY oim_product.meta_value, oi.order_item_name";

        return $sql;
    }

    /**
     * Applica l'ordinamento ai risultati
     */
    private function apply_sorting( $products_report, $sortby, $order ) {
        if ( $sortby === 'name' ) {
            uasort( $products_report, function( $a, $b ) use ( $order ) {
                $result = strcmp( $a['name'], $b['name'] );
                return ( $order === 'asc' ) ? $result : -$result;
            });
        } elseif ( $sortby === 'quantity' ) {
            uasort( $products_report, function( $a, $b ) use ( $order ) {
                $result = $a['quantity'] - $b['quantity'];
                return ( $order === 'asc' ) ? $result : -$result;
            });
        }
        return $products_report;
    }

    /**
     * Renderizza i risultati del report con paginazione
     */
    private function render_report_results( $paged_results, $current_page, $total_pages, $total_items ) {
        ?>
        <h2><?php _e( 'Risultati del Report', 'wc-order-report' ); ?></h2>

        <?php if ( $total_items > 0 ) : ?>
            <p class="description">
                <?php printf( __( 'Trovati %d prodotti. Pagina %d di %d.', 'wc-order-report' ), $total_items, $current_page, $total_pages ); ?>
            </p>
        <?php endif; ?>

        <!-- Pulsante per esportare la tabella in PDF -->
        <p>
            <button id="export-pdf-button" class="button button-primary"><?php _e( 'Esporta in PDF', 'wc-order-report' ); ?></button>
        </p>

        <table id="wc-order-report-table" class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e( 'Nome Prodotto', 'wc-order-report' ); ?></th>
                    <th><?php _e( 'Quantità Acquistata', 'wc-order-report' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ( ! empty( $paged_results ) ) : ?>
                    <?php foreach ( $paged_results as $product ) : ?>
                        <tr>
                            <td><?php echo esc_html( $product['name'] ); ?></td>
                            <td><?php echo esc_html( $product['quantity'] ); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="2"><?php _e( 'Nessun prodotto trovato per i criteri selezionati.', 'wc-order-report' ); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <?php if ( $total_pages > 1 ) : ?>
            <div class="tablenav">
                <div class="tablenav-pages">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg( 'paged', '%#%' ),
                        'format' => '',
                        'prev_text' => __( '&laquo;' ),
                        'next_text' => __( '&raquo;' ),
                        'total' => $total_pages,
                        'current' => $current_page,
                        'type' => 'plain'
                    );
                    echo paginate_links( $pagination_args );
                    ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Pulsante per esportare la tabella in PDF -->
        <p style="margin-top:10px;">
            <button id="export-pdf-button-two" class="button button-primary"><?php _e( 'Esporta in PDF', 'wc-order-report' ); ?></button>
        </p>

        <?php
    }

    /**
     * Handler AJAX per ottenere dati del report (per future implementazioni)
     */
    public function ajax_get_report_data() {
        // Verifica nonce per sicurezza
        if ( ! wp_verify_nonce( $_POST['nonce'], 'wc_order_report_nonce' ) ) {
            wp_die( 'Security check failed' );
        }

        // Implementazione futura per caricamento AJAX
        wp_die();
    }

    /**
     * Pulisce la cache del plugin
     */
    public function clear_report_cache() {
        global $wpdb;

        // Elimina tutti i transient del plugin
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_order_report_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_wc_order_report_%'" );
    }

    /**
     * Hook per pulire la cache quando un ordine viene aggiornato
     */
    public function maybe_clear_cache_on_order_update( $order_id ) {
        // Pulisce la cache quando un ordine viene modificato
        $this->clear_report_cache();
    }

    /**
     * Attiva gli hook per la pulizia automatica della cache
     */
    private function setup_cache_hooks() {
        // Pulisce la cache quando gli ordini vengono modificati
        add_action( 'woocommerce_order_status_changed', array( $this, 'maybe_clear_cache_on_order_update' ) );
        add_action( 'woocommerce_new_order', array( $this, 'maybe_clear_cache_on_order_update' ) );
    }
}

// Istanzia il plugin
new WC_Order_Report_Plugin();

// Includi il file di test performance solo in ambiente di sviluppo
if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
    require_once plugin_dir_path( __FILE__ ) . 'test-performance.php';
}
