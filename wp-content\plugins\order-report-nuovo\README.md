# WooCommerce Order Report - Plugin Ottimizzato

## Descrizione

Plugin WordPress ottimizzato per WooCommerce che genera report dettagliati sui prodotti venduti. La versione 2.0 include significative ottimizzazioni di performance per gestire efficacemente database con migliaia di ordini.

## Caratteristiche Principali

### ✅ Performance Ottimizzate
- **Query SQL dirette** invece di API WooCommerce per performance superiori
- **Sistema di cache** con WordPress transients
- **Paginazione** per gestire grandi dataset
- **Riduzione drastica** delle query al database (da N+1 a 1 singola query)

### ✅ Filtri Avanzati
- **Filtro per data**: Seleziona intervallo temporale specifico
- **Filtro per stato ordine**: Filtra per stati ordine multipli
- **Filtro per categorie**: Seleziona categorie prodotto specifiche
- **Ordinamento**: Per nome prodotto o quantità venduta

### ✅ Export e Visualizzazione
- **Export PDF**: Esporta i risultati in formato PDF
- **Tabella interattiva**: Con DataTables per ordinamento e ricerca
- **Paginazione**: Navigazione fluida tra i risultati
- **Contatori**: Visualizza totale prodotti e pagine

### ✅ Sicurezza e Affidabilità
- **Protezione CSRF** con nonce WordPress
- **Sanitizzazione input** completa
- **Gestione errori** robusta
- **Logging** degli errori SQL

## Installazione

1. Carica la cartella del plugin in `/wp-content/plugins/`
2. Attiva il plugin dal pannello WordPress
3. Vai su **Report Ordini** nel menu amministratore

## Utilizzo

### Generazione Report

1. **Seleziona Date**: Imposta data iniziale e finale
2. **Scegli Stati Ordine**: Seleziona gli stati ordine da includere
3. **Filtra Categorie**: (Opzionale) Seleziona categorie prodotto specifiche
4. **Imposta Ordinamento**: Scegli criterio e direzione di ordinamento
5. **Clicca "Genera Report"**

### Navigazione Risultati

- **Paginazione**: Usa i link di navigazione per spostarti tra le pagine
- **Ordinamento**: Clicca sulle intestazioni colonne per ordinare
- **Export PDF**: Usa il pulsante per esportare la pagina corrente

## Configurazione

### Parametri Modificabili

Nel file `wc-order-report.php`, puoi modificare:

```php
private $results_per_page = 100;  // Risultati per pagina
private $cache_expiry = 3600;     // Durata cache (secondi)
```

### Cache

Il plugin utilizza il sistema di cache WordPress:
- **Durata**: 1 ora (configurabile)
- **Auto-invalidazione**: Quando gli ordini vengono modificati
- **Chiave cache**: Basata sui filtri applicati

## Ottimizzazioni Tecniche

### Query Database
- Utilizza JOIN ottimizzati invece di query multiple
- Filtra a livello database invece che in PHP
- Usa indici esistenti di WooCommerce

### Gestione Memoria
- Carica solo i dati necessari per la pagina corrente
- Evita il caricamento di tutti gli ordini in memoria
- Processamento efficiente dei risultati

### Performance
- Cache intelligente per query ripetute
- Paginazione per ridurre il carico
- Query SQL ottimizzate per grandi dataset

## Requisiti di Sistema

- **WordPress**: 5.0 o superiore
- **WooCommerce**: 3.0 o superiore  
- **PHP**: 7.4 o superiore
- **MySQL**: 5.7 o superiore / MariaDB 10.2+

## Compatibilità

### Plugin Testati
- ✅ WooCommerce 8.x
- ✅ WordPress 6.x
- ✅ PHP 8.x
- ✅ Temi WordPress standard

### Limitazioni Note
- Export PDF limitato alla pagina corrente
- Cache invalidata ad ogni modifica ordine
- Richiede permessi `manage_options`

## Troubleshooting

### Problemi Comuni

**Report vuoto o errori**
- Verifica che WooCommerce sia attivo
- Controlla i permessi utente
- Verifica la presenza di ordini nel periodo selezionato

**Performance lente**
- Controlla la configurazione cache
- Verifica gli indici database
- Riduci l'intervallo di date

**Errori di memoria**
- Aumenta `memory_limit` PHP
- Riduci `$results_per_page`
- Controlla i log WordPress

### Log e Debug

Gli errori SQL vengono registrati nei log WordPress:
```php
error_log('WC Order Report SQL Error: ' . $wpdb->last_error);
```

Abilita il debug WordPress per maggiori dettagli:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Supporto

### File di Supporto
- `OTTIMIZZAZIONI.md`: Dettagli tecnici delle ottimizzazioni
- `wc-order-report.php`: File principale del plugin
- `wc-order-report.js`: Script frontend
- `wc-order-report.css`: Stili personalizzati

### Contatti
- **Autore**: Giovanni JoJo Castaldo
- **Versione**: 2.0
- **Licenza**: GPL v2 o superiore

## Changelog

### Versione 2.0
- ✅ Refactor completo per performance
- ✅ Query SQL ottimizzate
- ✅ Sistema di cache implementato
- ✅ Paginazione aggiunta
- ✅ Sicurezza migliorata
- ✅ Gestione errori robusta

### Versione 1.5 (Precedente)
- Implementazione base
- Query WooCommerce API
- Export PDF
- Filtri base

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o superiore.

## Contributi

Per segnalazioni bug o richieste di funzionalità, contatta l'autore o crea un issue nel repository del progetto.
