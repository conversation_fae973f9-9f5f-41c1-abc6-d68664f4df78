<?php
/**
 * Test file for new chatbot features
 * 
 * This file tests the new functionality added to the JoJo WC Chatbot plugin:
 * - Delivery time information
 * - Delivery estimate for specific orders
 * - Refund information
 * 
 * To run this test, access it via browser: /wp-content/plugins/jojo-wc-chatbot/test-new-features.php
 */

// Simulate WordPress environment for testing
define('ABSPATH', '../../../');
require_once ABSPATH . 'wp-config.php';
require_once ABSPATH . 'wp-includes/wp-db.php';
require_once ABSPATH . 'wp-includes/pluggable.php';

// Load plugin files
require_once 'includes/admin/class-admin-settings.php';
require_once 'includes/utils/class-openai-integration.php';
require_once 'includes/utils/class-intent-router.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>JoJo WC Chatbot - Test Nuove Funzionalità</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-input { margin: 10px 0; }
        .test-input input { padding: 8px; width: 300px; margin-right: 10px; }
        .test-input button { padding: 8px 15px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>JoJo WC Chatbot - Test Nuove Funzionalità</h1>
    
    <div class="test-section info">
        <h2>📋 Funzionalità Implementate</h2>
        <ul>
            <li><strong>Tempi di consegna generici:</strong> Risponde con i tempi medi configurati nelle impostazioni</li>
            <li><strong>Stima consegna ordine specifico:</strong> Calcola la data di consegna prevista per un ordine</li>
            <li><strong>Informazioni rimborsi:</strong> Fornisce il link alla pagina rimborsi configurata</li>
            <li><strong>Router degli intenti migliorato:</strong> Riconosce meglio le intenzioni dell'utente</li>
        </ul>
    </div>

    <?php
    // Test 1: Verifica impostazioni
    echo '<div class="test-section">';
    echo '<h2>🔧 Test 1: Verifica Impostazioni</h2>';
    
    try {
        $average_days = get_option(JoJo_WC_Chatbot_Admin_Settings::OPT_AVERAGE_DELIVERY_DAYS, 3);
        $max_days = get_option(JoJo_WC_Chatbot_Admin_Settings::OPT_MAX_DELIVERY_DAYS, 7);
        $refund_url = get_option(JoJo_WC_Chatbot_Admin_Settings::OPT_REFUND_PAGE_URL, '');
        
        echo '<div class="success">';
        echo '<p><strong>✅ Impostazioni caricate correttamente:</strong></p>';
        echo '<ul>';
        echo '<li>Tempi medi di consegna: ' . $average_days . ' giorni</li>';
        echo '<li>Tempo massimo di consegna: ' . $max_days . ' giorni</li>';
        echo '<li>URL pagina rimborsi: ' . ($refund_url ? $refund_url : 'Non configurato') . '</li>';
        echo '</ul>';
        echo '</div>';
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<p><strong>❌ Errore nel caricamento delle impostazioni:</strong></p>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '</div>';
    }
    echo '</div>';

    // Test 2: Test Router degli Intenti
    echo '<div class="test-section">';
    echo '<h2>🎯 Test 2: Router degli Intenti</h2>';
    
    $test_messages = array(
        'quanto ci vuole per la consegna?' => 'DELIVERY_TIME',
        'quando arriva il mio ordine 1234?' => 'DELIVERY_ESTIMATE',
        'come faccio a restituire un prodotto?' => 'REFUND_INFO',
        'voglio il rimborso' => 'REFUND_INFO',
        'stato ordine 5678' => 'ORDER_STATUS',
        'cerco cuffie bluetooth' => 'RECOMMEND_PRODUCTS',
        'ciao' => 'SMALL_TALK',
        'che tempo fa?' => 'OTHER'
    );
    
    echo '<p><strong>Messaggi di test e intenti attesi:</strong></p>';
    echo '<ul>';
    foreach ($test_messages as $message => $expected_intent) {
        echo '<li><code>' . $message . '</code> → <strong>' . $expected_intent . '</strong></li>';
    }
    echo '</ul>';
    echo '</div>';

    // Test 3: Test Funzioni di Consegna
    echo '<div class="test-section">';
    echo '<h2>🚚 Test 3: Funzioni di Consegna</h2>';
    
    try {
        $openai = new JoJo_WC_Chatbot_OpenAI_Integration();
        
        // Test get_delivery_info
        $reflection = new ReflectionClass($openai);
        $method = $reflection->getMethod('get_delivery_info');
        $method->setAccessible(true);
        $result = $method->invoke($openai, array());
        
        echo '<div class="success">';
        echo '<p><strong>✅ Test get_delivery_info:</strong></p>';
        echo '<pre>' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<p><strong>❌ Errore nel test delle funzioni di consegna:</strong></p>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '</div>';
    }
    echo '</div>';

    // Test 4: Test Funzioni di Rimborso
    echo '<div class="test-section">';
    echo '<h2>💰 Test 4: Funzioni di Rimborso</h2>';
    
    try {
        $openai = new JoJo_WC_Chatbot_OpenAI_Integration();
        
        // Test get_refund_info
        $reflection = new ReflectionClass($openai);
        $method = $reflection->getMethod('get_refund_info');
        $method->setAccessible(true);
        $result = $method->invoke($openai, array());
        
        echo '<div class="success">';
        echo '<p><strong>✅ Test get_refund_info:</strong></p>';
        echo '<pre>' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<p><strong>❌ Errore nel test delle funzioni di rimborso:</strong></p>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '</div>';
    }
    echo '</div>';

    // Test 5: Test Router Completo
    echo '<div class="test-section">';
    echo '<h2>🤖 Test 5: Test Router Completo</h2>';
    
    try {
        $router = new JoJo_WC_Chatbot_Intent_Router();
        
        // Test con messaggio sui tempi di consegna
        $test_message = "quanto ci vuole per la consegna?";
        $result = $router->route_message($test_message);
        
        echo '<div class="success">';
        echo '<p><strong>✅ Test router con messaggio: "' . $test_message . '"</strong></p>';
        echo '<pre>' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<p><strong>❌ Errore nel test del router:</strong></p>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '</div>';
    }
    echo '</div>';
    ?>

    <div class="test-section info">
        <h2>📝 Istruzioni per il Test Completo</h2>
        <ol>
            <li><strong>Configura le impostazioni:</strong> Vai in WordPress Admin → Impostazioni → JoJo Chatbot</li>
            <li><strong>Imposta i valori:</strong>
                <ul>
                    <li>Link pagina rimborsi: inserisci un URL valido</li>
                    <li>Tempi medi di consegna: es. 3 giorni</li>
                    <li>Tempo massimo di consegna: es. 7 giorni</li>
                </ul>
            </li>
            <li><strong>Testa il chatbot:</strong> Usa lo shortcode [jojo_wc_chat] in una pagina</li>
            <li><strong>Prova questi messaggi:</strong>
                <ul>
                    <li>"Quanto ci vuole per la consegna?"</li>
                    <li>"Come faccio a restituire un prodotto?"</li>
                    <li>"Voglio il rimborso"</li>
                    <li>"Quando arriva il mio ordine 123?" (se hai ordini di test)</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎉 Riepilogo Implementazione</h2>
        <p><strong>Le seguenti funzionalità sono state implementate con successo:</strong></p>
        <ul>
            <li>✅ Nuove opzioni nelle impostazioni del plugin</li>
            <li>✅ Router degli intenti aggiornato con nuovi intenti</li>
            <li>✅ Funzioni per tempi di consegna generici e specifici</li>
            <li>✅ Funzioni per informazioni sui rimborsi</li>
            <li>✅ Integrazione completa nel sistema esistente</li>
        </ul>
        <p><strong>Il chatbot ora può rispondere a:</strong></p>
        <ul>
            <li>🚚 Domande sui tempi di consegna generici</li>
            <li>📦 Richieste di stima consegna per ordini specifici</li>
            <li>💰 Domande sui rimborsi e procedure di reso</li>
            <li>📋 Stato degli ordini (funzionalità esistente)</li>
            <li>🛍️ Consigli sui prodotti (funzionalità esistente)</li>
        </ul>
    </div>
</body>
</html>
