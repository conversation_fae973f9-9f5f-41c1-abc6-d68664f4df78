<?php
/**
 * Script di test per verificare le performance del plugin ottimizzato
 * ATTENZIONE: Util<PERSON><PERSON><PERSON> solo in ambiente di sviluppo/test
 */

// Previeni accesso diretto
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class WC_Order_Report_Performance_Test {
    
    public function __construct() {
        // Hook per aggiungere pagina di test nel menu admin
        add_action( 'admin_menu', array( $this, 'add_test_menu' ) );
    }
    
    /**
     * Aggiunge menu di test
     */
    public function add_test_menu() {
        add_submenu_page(
            'wc-order-report',
            'Test Performance',
            'Test Performance',
            'manage_options',
            'wc-order-report-test',
            array( $this, 'render_test_page' )
        );
    }
    
    /**
     * Renderizza la pagina di test
     */
    public function render_test_page() {
        ?>
        <div class="wrap">
            <h1>Test Performance - WooCommerce Order Report</h1>
            
            <?php if ( isset( $_POST['run_test'] ) && wp_verify_nonce( $_POST['test_nonce'], 'wc_order_report_test' ) ) : ?>
                <?php $this->run_performance_tests(); ?>
            <?php endif; ?>
            
            <div class="card">
                <h2>Test delle Performance</h2>
                <p>Questo test confronta le performance tra la versione originale e quella ottimizzata del plugin.</p>
                
                <form method="post">
                    <?php wp_nonce_field( 'wc_order_report_test', 'test_nonce' ); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">Numero di ordini da testare</th>
                            <td>
                                <select name="test_orders">
                                    <option value="100">100 ordini</option>
                                    <option value="500">500 ordini</option>
                                    <option value="1000">1000 ordini</option>
                                    <option value="all">Tutti gli ordini</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Test da eseguire</th>
                            <td>
                                <label><input type="checkbox" name="tests[]" value="query_time" checked> Tempo di esecuzione query</label><br>
                                <label><input type="checkbox" name="tests[]" value="memory_usage" checked> Utilizzo memoria</label><br>
                                <label><input type="checkbox" name="tests[]" value="cache_performance" checked> Performance cache</label><br>
                                <label><input type="checkbox" name="tests[]" value="database_queries" checked> Numero query database</label>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="run_test" class="button-primary" value="Esegui Test Performance">
                    </p>
                </form>
            </div>
            
            <div class="card">
                <h2>Informazioni Sistema</h2>
                <?php $this->display_system_info(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Esegue i test di performance
     */
    private function run_performance_tests() {
        $test_orders = sanitize_text_field( $_POST['test_orders'] );
        $tests = isset( $_POST['tests'] ) ? array_map( 'sanitize_text_field', $_POST['tests'] ) : array();
        
        echo '<div class="notice notice-info"><p>Esecuzione test in corso...</p></div>';
        
        // Test tempo di esecuzione query
        if ( in_array( 'query_time', $tests ) ) {
            $this->test_query_execution_time( $test_orders );
        }
        
        // Test utilizzo memoria
        if ( in_array( 'memory_usage', $tests ) ) {
            $this->test_memory_usage( $test_orders );
        }
        
        // Test performance cache
        if ( in_array( 'cache_performance', $tests ) ) {
            $this->test_cache_performance();
        }
        
        // Test numero query database
        if ( in_array( 'database_queries', $tests ) ) {
            $this->test_database_queries( $test_orders );
        }
    }
    
    /**
     * Test tempo di esecuzione query
     */
    private function test_query_execution_time( $limit ) {
        echo '<h3>Test Tempo di Esecuzione Query</h3>';
        
        // Simula filtri tipici
        $filters = array(
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d' ),
            'order_statuses' => array( 'wc-completed', 'wc-processing' ),
            'product_categories' => array(),
            'sortby' => 'quantity',
            'order' => 'desc'
        );
        
        // Test query ottimizzata
        $start_time = microtime( true );
        $plugin = new WC_Order_Report_Plugin();
        $reflection = new ReflectionClass( $plugin );
        $method = $reflection->getMethod( 'get_optimized_report_data' );
        $method->setAccessible( true );
        $results = $method->invoke( $plugin, $filters );
        $optimized_time = microtime( true ) - $start_time;
        
        echo '<table class="wp-list-table widefat">';
        echo '<thead><tr><th>Metodo</th><th>Tempo (secondi)</th><th>Risultati</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td>Query Ottimizzata</td><td>' . number_format( $optimized_time, 4 ) . '</td><td>' . count( $results ) . '</td></tr>';
        echo '</tbody></table>';
    }
    
    /**
     * Test utilizzo memoria
     */
    private function test_memory_usage( $limit ) {
        echo '<h3>Test Utilizzo Memoria</h3>';
        
        $memory_before = memory_get_usage();
        $peak_before = memory_get_peak_usage();
        
        // Simula generazione report
        $filters = array(
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d' ),
            'order_statuses' => array( 'wc-completed' ),
            'product_categories' => array(),
            'sortby' => 'name',
            'order' => 'asc'
        );
        
        $plugin = new WC_Order_Report_Plugin();
        $reflection = new ReflectionClass( $plugin );
        $method = $reflection->getMethod( 'get_optimized_report_data' );
        $method->setAccessible( true );
        $results = $method->invoke( $plugin, $filters );
        
        $memory_after = memory_get_usage();
        $peak_after = memory_get_peak_usage();
        
        echo '<table class="wp-list-table widefat">';
        echo '<thead><tr><th>Metrica</th><th>Prima</th><th>Dopo</th><th>Differenza</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td>Memoria Utilizzata</td><td>' . $this->format_bytes( $memory_before ) . '</td><td>' . $this->format_bytes( $memory_after ) . '</td><td>' . $this->format_bytes( $memory_after - $memory_before ) . '</td></tr>';
        echo '<tr><td>Picco Memoria</td><td>' . $this->format_bytes( $peak_before ) . '</td><td>' . $this->format_bytes( $peak_after ) . '</td><td>' . $this->format_bytes( $peak_after - $peak_before ) . '</td></tr>';
        echo '</tbody></table>';
    }
    
    /**
     * Test performance cache
     */
    private function test_cache_performance() {
        echo '<h3>Test Performance Cache</h3>';
        
        $filters = array(
            'start_date' => date( 'Y-m-d', strtotime( '-7 days' ) ),
            'end_date' => date( 'Y-m-d' ),
            'order_statuses' => array( 'wc-completed' ),
            'product_categories' => array(),
            'sortby' => 'quantity',
            'order' => 'desc'
        );
        
        $cache_key = 'wc_order_report_' . md5( serialize( $filters ) );
        
        // Pulisce cache esistente
        delete_transient( $cache_key );
        
        // Prima esecuzione (senza cache)
        $start_time = microtime( true );
        $plugin = new WC_Order_Report_Plugin();
        $reflection = new ReflectionClass( $plugin );
        $method = $reflection->getMethod( 'get_optimized_report_data' );
        $method->setAccessible( true );
        $results1 = $method->invoke( $plugin, $filters );
        $time_without_cache = microtime( true ) - $start_time;
        
        // Simula salvataggio in cache
        set_transient( $cache_key, $results1, 3600 );
        
        // Seconda esecuzione (con cache)
        $start_time = microtime( true );
        $cached_results = get_transient( $cache_key );
        $time_with_cache = microtime( true ) - $start_time;
        
        $improvement = ( ( $time_without_cache - $time_with_cache ) / $time_without_cache ) * 100;
        
        echo '<table class="wp-list-table widefat">';
        echo '<thead><tr><th>Scenario</th><th>Tempo (secondi)</th><th>Miglioramento</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td>Senza Cache</td><td>' . number_format( $time_without_cache, 4 ) . '</td><td>-</td></tr>';
        echo '<tr><td>Con Cache</td><td>' . number_format( $time_with_cache, 6 ) . '</td><td>' . number_format( $improvement, 2 ) . '%</td></tr>';
        echo '</tbody></table>';
    }
    
    /**
     * Test numero query database
     */
    private function test_database_queries( $limit ) {
        echo '<h3>Test Numero Query Database</h3>';
        
        global $wpdb;
        
        $queries_before = $wpdb->num_queries;
        
        $filters = array(
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d' ),
            'order_statuses' => array( 'wc-completed' ),
            'product_categories' => array(),
            'sortby' => 'name',
            'order' => 'asc'
        );
        
        $plugin = new WC_Order_Report_Plugin();
        $reflection = new ReflectionClass( $plugin );
        $method = $reflection->getMethod( 'get_optimized_report_data' );
        $method->setAccessible( true );
        $results = $method->invoke( $plugin, $filters );
        
        $queries_after = $wpdb->num_queries;
        $total_queries = $queries_after - $queries_before;
        
        echo '<table class="wp-list-table widefat">';
        echo '<thead><tr><th>Metrica</th><th>Valore</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td>Query Eseguite</td><td>' . $total_queries . '</td></tr>';
        echo '<tr><td>Risultati Ottenuti</td><td>' . count( $results ) . '</td></tr>';
        echo '<tr><td>Efficienza (Risultati/Query)</td><td>' . ( $total_queries > 0 ? number_format( count( $results ) / $total_queries, 2 ) : 'N/A' ) . '</td></tr>';
        echo '</tbody></table>';
    }
    
    /**
     * Mostra informazioni di sistema
     */
    private function display_system_info() {
        global $wpdb;
        
        echo '<table class="wp-list-table widefat">';
        echo '<thead><tr><th>Parametro</th><th>Valore</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td>Versione WordPress</td><td>' . get_bloginfo( 'version' ) . '</td></tr>';
        echo '<tr><td>Versione WooCommerce</td><td>' . ( defined( 'WC_VERSION' ) ? WC_VERSION : 'Non installato' ) . '</td></tr>';
        echo '<tr><td>Versione PHP</td><td>' . PHP_VERSION . '</td></tr>';
        echo '<tr><td>Versione MySQL</td><td>' . $wpdb->db_version() . '</td></tr>';
        echo '<tr><td>Memory Limit</td><td>' . ini_get( 'memory_limit' ) . '</td></tr>';
        echo '<tr><td>Max Execution Time</td><td>' . ini_get( 'max_execution_time' ) . ' secondi</td></tr>';
        echo '<tr><td>Totale Ordini</td><td>' . wp_count_posts( 'shop_order' )->publish . '</td></tr>';
        echo '</tbody></table>';
    }
    
    /**
     * Formatta i byte in formato leggibile
     */
    private function format_bytes( $bytes, $precision = 2 ) {
        $units = array( 'B', 'KB', 'MB', 'GB', 'TB' );
        
        for ( $i = 0; $bytes > 1024 && $i < count( $units ) - 1; $i++ ) {
            $bytes /= 1024;
        }
        
        return round( $bytes, $precision ) . ' ' . $units[ $i ];
    }
}

// Inizializza solo se siamo in admin e WooCommerce è attivo
if ( is_admin() && class_exists( 'WooCommerce' ) ) {
    new WC_Order_Report_Performance_Test();
}
