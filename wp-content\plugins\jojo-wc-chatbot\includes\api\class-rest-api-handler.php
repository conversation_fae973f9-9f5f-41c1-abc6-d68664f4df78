<?php
/**
 * REST API Handler Class
 *
 * Handles all REST API routes and endpoints for the chatbot functionality.
 * Provides secure endpoints for chat interactions and API testing.
 *
 * @package Jo<PERSON><PERSON>_WC_Chatbot
 * @since 0.3.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class JoJo_WC_Chatbot_REST_API_Handler
 */
class JoJo_WC_Chatbot_REST_API_Handler {

    /**
     * OpenAI integration instance
     *
     * @var JoJo_WC_Chatbot_OpenAI_Integration
     */
    private $openai_integration;

    /**
     * Constructor
     */
    public function __construct() {
        $this->openai_integration = new JoJo_WC_Chatbot_OpenAI_Integration();
        add_action('rest_api_init', array($this, 'register_routes'));
    }

    /**
     * Register all REST API routes
     */
    public function register_routes() {
        // Public chat endpoint
        register_rest_route('jojo/v1', '/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_chat_request'),
            'permission_callback' => '__return_true',
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'validate_callback' => array($this, 'validate_message')
                )
            )
        ));

        // Internal LLM endpoint (with optional token protection)
        register_rest_route('jojo/v1', '/llm', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_llm_request'),
            'permission_callback' => array($this, 'check_public_token'),
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'validate_callback' => array($this, 'validate_message')
                )
            )
        ));

        // Self-test endpoint (admin only)
        register_rest_route('jojo/v1', '/selftest', array(
            'methods' => 'GET',
            'callback' => array($this, 'handle_selftest_request'),
            'permission_callback' => array($this, 'check_admin_permission')
        ));
    }

    /**
     * Handle chat request (public endpoint)
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_chat_request($request) {
        $message = $request->get_param('message');
        
        if (empty($message)) {
            return new WP_REST_Response(array(
                'reply' => __('Please write a question.', 'jojo-wc-chatbot')
            ), 200);
        }

        // Make internal request to LLM endpoint
        $internal_args = array(
            'headers' => array('Content-Type' => 'application/json'),
            'body' => wp_json_encode(array('message' => $message), JSON_UNESCAPED_UNICODE),
            'timeout' => 25,
        );

        // Add token if required
        $token = JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_PUBLIC_TOKEN
        );
        if (!empty($token)) {
            $internal_args['headers']['X-JOJO-TOKEN'] = $token;
        }

        $response = wp_remote_post(rest_url('jojo/v1/llm'), $internal_args);

        if (is_wp_error($response)) {
            return new WP_REST_Response(array(
                'reply' => __('LLM connection error.', 'jojo-wc-chatbot')
            ), 200);
        }

        $response_data = json_decode(wp_remote_retrieve_body($response), true);
        return new WP_REST_Response($response_data, 200);
    }

    /**
     * Handle LLM request (internal endpoint)
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_llm_request($request) {
        $message = $request->get_param('message');
        
        if (empty($message)) {
            return new WP_REST_Response(array(
                'reply' => __('Please write a question.', 'jojo-wc-chatbot')
            ), 200);
        }

        try {
            // Use intent router for better handling of new intents
            $response_data = $this->openai_integration->process_llm_request($message, true);
            return new WP_REST_Response($response_data, 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'reply' => __('An error occurred while processing your request.', 'jojo-wc-chatbot'),
                'error' => $e->getMessage()
            ), 200);
        }
    }

    /**
     * Handle self-test request (admin only)
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_selftest_request($request) {
        try {
            $test_results = $this->openai_integration->test_api_connection();
            return new WP_REST_Response($test_results, 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'ok' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    /**
     * Check public token permission
     *
     * @param WP_REST_Request $request Request object
     * @return bool Permission granted
     */
    public function check_public_token($request) {
        $required_token = JoJo_WC_Chatbot_Admin_Settings::get_option(
            JoJo_WC_Chatbot_Admin_Settings::OPT_PUBLIC_TOKEN
        );

        // If no token is set, allow access
        if (empty($required_token)) {
            return true;
        }

        $provided_token = $request->get_header('x-jojo-token');
        
        // Use hash_equals for timing-safe comparison
        return hash_equals($required_token, (string) $provided_token);
    }

    /**
     * Check admin permission
     *
     * @param WP_REST_Request $request Request object
     * @return bool Permission granted
     */
    public function check_admin_permission($request) {
        return current_user_can('manage_options');
    }

    /**
     * Validate message parameter
     *
     * @param string $value Message value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error Validation result
     */
    public function validate_message($value, $request, $param) {
        if (empty($value) || !is_string($value)) {
            return new WP_Error(
                'invalid_message',
                __('Message must be a non-empty string.', 'jojo-wc-chatbot'),
                array('status' => 400)
            );
        }

        if (strlen($value) > 1000) {
            return new WP_Error(
                'message_too_long',
                __('Message is too long. Maximum 1000 characters allowed.', 'jojo-wc-chatbot'),
                array('status' => 400)
            );
        }

        return true;
    }

    /**
     * Get REST API base URL
     *
     * @return string Base URL
     */
    public static function get_rest_base_url() {
        return rest_url('jojo/v1');
    }

    /**
     * Get chat endpoint URL
     *
     * @return string Chat endpoint URL
     */
    public static function get_chat_endpoint_url() {
        return rest_url('jojo/v1/chat');
    }

    /**
     * Get LLM endpoint URL
     *
     * @return string LLM endpoint URL
     */
    public static function get_llm_endpoint_url() {
        return rest_url('jojo/v1/llm');
    }

    /**
     * Get self-test endpoint URL
     *
     * @return string Self-test endpoint URL
     */
    public static function get_selftest_endpoint_url() {
        return rest_url('jojo/v1/selftest');
    }
}
